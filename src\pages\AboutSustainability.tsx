// import { Link } from 'react-router-dom';
// import Certifications from '../components/Certifications';

interface AboutSustainabilityProps {
  language: 'vi' | 'en';
}

const AboutSustainability = ({ language }: AboutSustainabilityProps) => {
  const translations = {
    pageTitle: {
      vi: 'PHÁT TRIỂN BỀN VỮNG',
      en: 'SUSTAINABILITY'
    },
    heroTitle: {
      vi: 'CAM KẾT VỚI MÔI TRƯỜNG VÀ XÃ HỘI',
      en: 'OUR COMMITMENT TO ENVIRONMENT AND SOCIETY'
    },
    heroContent: {
      vi: 'Chúng tôi tin rằng kinh doanh bền vững là nền tảng cho sự phát triển lâu dài',
      en: 'We believe that sustainable business is the foundation for long-term development'
    },
    ourApproach: {
      title: {
        vi: 'CÁCH TIẾP CẬN CỦA CHÚNG TÔI',
        en: 'OUR APPROACH'
      },
      content: {
        vi: 'Tại AN BÌNH FOODS, phát triển bền vững không chỉ là một khẩu hiệu mà là một phần không thể thiếu trong mọi hoạt động kinh doanh của chúng tôi. Chúng tôi hiểu rằng sự thành công lâu dài phụ thuộc vào khả năng cân bằng giữa lợi nhuận kinh tế, trách nhiệm môi trường và phúc lợi xã hội.\n\nChúng tôi áp dụng cách tiếp cận toàn diện đối với tính bền vững, từ cách chúng tôi trồng và thu hoạch trái cây, đến cách chúng tôi đóng gói, vận chuyển và phân phối sản phẩm đến tay người tiêu dùng.',
        en: 'At AN BINH FOODS, sustainability is not just a slogan but an integral part of all our business operations. We understand that long-term success depends on the ability to balance economic profit, environmental responsibility, and social welfare.\n\nWe apply a comprehensive approach to sustainability, from how we grow and harvest fruits, to how we package, transport, and distribute products to consumers.'
      }
    },
    environmentalInitiatives: {
      title: {
        vi: 'SÁNG KIẾN MÔI TRƯỜNG',
        en: 'ENVIRONMENTAL INITIATIVES'
      },
      content: {
        vi: 'Chúng tôi cam kết giảm thiểu tác động môi trường trong toàn bộ chuỗi cung ứng của mình. Một số sáng kiến chính của chúng tôi bao gồm:\n\n- Thực hành nông nghiệp bền vững: Hợp tác với nông dân áp dụng các phương pháp canh tác thân thiện với môi trường, giảm thiểu sử dụng hóa chất và bảo tồn đa dạng sinh học.\n\n- Quản lý nước: Triển khai các hệ thống tưới tiêu hiệu quả và xử lý nước thải để bảo vệ nguồn nước quý giá.\n\n- Giảm thiểu chất thải: Áp dụng nguyên tắc giảm thiểu, tái sử dụng và tái chế trong toàn bộ hoạt động, đặc biệt là trong đóng gói sản phẩm.\n\n- Năng lượng tái tạo: Đầu tư vào năng lượng mặt trời và các nguồn năng lượng tái tạo khác tại các cơ sở sản xuất và kho bãi.',
        en: 'We are committed to minimizing environmental impact throughout our supply chain. Some of our key initiatives include:\n\n- Sustainable agricultural practices: Partnering with farmers to implement environmentally friendly farming methods, minimizing chemical use, and preserving biodiversity.\n\n- Water management: Implementing efficient irrigation systems and wastewater treatment to protect valuable water resources.\n\n- Waste reduction: Applying the principles of reduce, reuse, and recycle throughout our operations, especially in product packaging.\n\n- Renewable energy: Investing in solar energy and other renewable energy sources at our production facilities and warehouses.'
      }
    },
    socialResponsibility: {
      title: {
        vi: 'TRÁCH NHIỆM XÃ HỘI',
        en: 'SOCIAL RESPONSIBILITY'
      },
      content: {
        vi: 'Chúng tôi tin rằng thành công trong kinh doanh đi đôi với trách nhiệm đối với cộng đồng. Các chương trình trách nhiệm xã hội của chúng tôi tập trung vào:\n\n- Hỗ trợ nông dân: Cung cấp đào tạo, hỗ trợ kỹ thuật và đảm bảo thu nhập ổn định cho nông dân đối tác.\n\n- Phát triển cộng đồng: Đầu tư vào giáo dục, y tế và cơ sở hạ tầng tại các cộng đồng nơi chúng tôi hoạt động.\n\n- Điều kiện làm việc công bằng: Đảm bảo môi trường làm việc an toàn, lương thưởng công bằng và cơ hội phát triển cho tất cả nhân viên.\n\n- An toàn thực phẩm: Cam kết cung cấp sản phẩm an toàn, chất lượng cao cho người tiêu dùng.',
        en: 'We believe that business success goes hand in hand with responsibility to the community. Our social responsibility programs focus on:\n\n- Supporting farmers: Providing training, technical support, and ensuring stable income for partner farmers.\n\n- Community development: Investing in education, healthcare, and infrastructure in the communities where we operate.\n\n- Fair working conditions: Ensuring safe working environments, fair compensation, and development opportunities for all employees.\n\n- Food safety: Commitment to providing safe, high-quality products to consumers.'
      }
    },
    certifications: {
      title: {
        vi: 'CHỨNG NHẬN VÀ TIÊU CHUẨN',
        en: 'CERTIFICATIONS AND STANDARDS'
      },
      content: {
        vi: 'Để đảm bảo cam kết của mình với phát triển bền vững, chúng tôi tuân thủ các tiêu chuẩn quốc tế nghiêm ngặt và đã đạt được nhiều chứng nhận quan trọng:\n\n- Chứng nhận GlobalG.A.P: Đảm bảo thực hành nông nghiệp tốt trên toàn cầu\n\n- Chứng nhận ISO 14001: Hệ thống quản lý môi trường\n\n- Chứng nhận HACCP và ISO 22000: Đảm bảo an toàn thực phẩm\n\n- Chứng nhận Thương mại Công bằng (Fair Trade): Đảm bảo thương mại công bằng và điều kiện làm việc tốt',
        en: 'To ensure our commitment to sustainable development, we adhere to strict international standards and have achieved several important certifications:\n\n- GlobalG.A.P Certification: Ensuring good agricultural practices globally\n\n- ISO 14001 Certification: Environmental management system\n\n- HACCP and ISO 22000 Certification: Ensuring food safety\n\n- Fair Trade Certification: Ensuring fair trade and good working conditions'
      }
    },
    futureGoals: {
      title: {
        vi: 'MỤC TIÊU TƯƠNG LAI',
        en: 'FUTURE GOALS'
      },
      content: {
        vi: 'Chúng tôi không ngừng nỗ lực cải thiện hiệu suất bền vững của mình. Các mục tiêu tương lai của chúng tôi bao gồm:\n\n- Giảm 50% lượng khí thải carbon vào năm 2030\n\n- Đạt được 100% bao bì có thể tái chế hoặc phân hủy sinh học vào năm 2025\n\n- Mở rộng các chương trình hỗ trợ nông dân đến 5.000 hộ gia đình vào năm 2027\n\n- Đạt được chứng nhận B Corp, khẳng định cam kết của chúng tôi với các tiêu chuẩn cao nhất về hiệu suất xã hội và môi trường',
        en: 'We continuously strive to improve our sustainability performance. Our future goals include:\n\n- Reducing carbon emissions by 50% by 2030\n\n- Achieving 100% recyclable or biodegradable packaging by 2025\n\n- Expanding farmer support programs to 5,000 households by 2027\n\n- Achieving B Corp certification, affirming our commitment to the highest standards of social and environmental performance'
      }
    }
  };

  return (
    <div>
      {/* Hero Section with Video Background */}
      <section className="relative h-[400px] overflow-hidden">
        <video
          className="absolute w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
        >
          <source src="/video/video1.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        <div className="absolute inset-0 bg-black bg-opacity-60"></div>
        <div className="absolute inset-0 flex flex-col items-center justify-center text-white text-center px-4">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            {translations.pageTitle[language]}
          </h1>
          <div className="w-20 h-1 bg-green-600 mb-6"></div>
          <h2 className="text-2xl md:text-3xl font-semibold mb-2">
            {translations.heroTitle[language]}
          </h2>
          <p className="text-xl max-w-2xl">
            {translations.heroContent[language]}
          </p>
        </div>
      </section>

      {/* Our Approach Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
              <img
                src="https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80"
                alt="Sustainable Approach"
                className="rounded-lg shadow-lg w-full h-auto"
              />
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6 text-green-600">
                {translations.ourApproach.title[language]}
              </h2>
              <div className="text-gray-700 space-y-4">
                {translations.ourApproach.content[language].split('\n\n').map((paragraph, index) => (
                  <p key={index} className="text-lg leading-relaxed">
                    {paragraph}
                  </p>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Environmental Initiatives Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row-reverse items-center">
            <div className="md:w-1/2 mb-8 md:mb-0 md:pl-8">
              <img
                src="/images/about/DJI_0025.jpg"
                alt="Environmental Initiatives"
                className="rounded-lg shadow-lg w-full h-auto"
              />
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6 text-green-600">
                {translations.environmentalInitiatives.title[language]}
              </h2>
              <div className="text-gray-700 space-y-4">
                {translations.environmentalInitiatives.content[language].split('\n\n').map((paragraph, index) => (
                  <div key={index}>
                    {paragraph.includes('-') ? (
                      <ul className="list-disc pl-5 space-y-2">
                        {paragraph.split('\n').map((item, i) => (
                          <li key={i} className="text-lg leading-relaxed">
                            {item.replace('- ', '')}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-lg leading-relaxed">{paragraph}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Social Responsibility Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
              <img
                src="https://images.unsplash.com/photo-1560493676-04071c5f467b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80"
                alt="Social Responsibility"
                className="rounded-lg shadow-lg w-full h-auto"
              />
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6 text-green-600">
                {translations.socialResponsibility.title[language]}
              </h2>
              <div className="text-gray-700 space-y-4">
                {translations.socialResponsibility.content[language].split('\n\n').map((paragraph, index) => (
                  <div key={index}>
                    {paragraph.includes('-') ? (
                      <ul className="list-disc pl-5 space-y-2">
                        {paragraph.split('\n').map((item, i) => (
                          <li key={i} className="text-lg leading-relaxed">
                            {item.replace('- ', '')}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-lg leading-relaxed">{paragraph}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Certifications Section */}
      {/* <Certifications language={language} /> */}

      {/* Future Goals Section */}
      {/* <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row-reverse items-center">
            <div className="md:w-1/2 mb-8 md:mb-0 md:pl-8">
              <img
                src="https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1473&q=80"
                alt="Future Goals"
                className="rounded-lg shadow-lg w-full h-auto"
              />
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6 text-green-600">
                {translations.futureGoals.title[language]}
              </h2>
              <div className="text-gray-700 space-y-4">
                {translations.futureGoals.content[language].split('\n\n').map((paragraph, index) => (
                  <div key={index}>
                    {paragraph.includes('-') ? (
                      <ul className="list-disc pl-5 space-y-2">
                        {paragraph.split('\n').map((item, i) => (
                          <li key={i} className="text-lg leading-relaxed">
                            {item.replace('- ', '')}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-lg leading-relaxed">{paragraph}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section> */}

      {/* Call to Action */}
      {/* <section className="py-16 bg-green-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">
            {language === 'vi' ? 'THAM GIA CÙNG CHÚNG TÔI' : 'JOIN US'}
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            {language === 'vi'
              ? 'Hãy cùng chúng tôi xây dựng một tương lai bền vững cho ngành trái cây và cho hành tinh của chúng ta.'
              : 'Join us in building a sustainable future for the fruit industry and for our planet.'}
          </p>
          <Link
            to="/contact"
            className="inline-block bg-white text-green-600 font-bold py-3 px-8 rounded-full hover:bg-gray-100 transition duration-300"
          >
            {language === 'vi' ? 'LIÊN HỆ NGAY' : 'CONTACT US NOW'}
          </Link>
        </div>
      </section> */}
    </div>
  );
};

export default AboutSustainability;
