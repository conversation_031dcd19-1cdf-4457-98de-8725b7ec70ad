import { useParams } from 'react-router-dom';
import ProductDetail from '../components/ProductDetail';
import { productData } from '../data/productData';

interface ProductDetailPageProps {
  language: 'vi' | 'en';
}

const ProductDetailPage = ({ language }: ProductDetailPageProps) => {
  const { categoryId, productId } = useParams<{ categoryId: string; productId: string }>();

  // Find the product in our data
  const product = productData.find(p => p.id === productId && p.category.id === categoryId);

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-16 text-center bg-[url('/images/assets/bg.jpg')]">
        <h1 className="text-2xl font-bold mb-4">
          {language === 'vi' ? 'Không tìm thấy sản phẩm' : 'Product not found'}
        </h1>
        <p>
          {language === 'vi'
            ? 'Sản phẩm bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.'
            : 'The product you are looking for does not exist or has been removed.'}
        </p>
      </div>
    );
  }

  return <ProductDetail language={language} product={product} />;
};

export default ProductDetailPage;
