import { Link } from 'react-router-dom';

interface ProductCategoryProps {
  language: 'vi' | 'en';
  category: {
    id: string;
    name: {
      vi: string;
      en: string;
    };
    products: {
      id: string;
      name: {
        vi: string;
        en: string;
      };
      slug: string;
    }[];
  };
}

const ProductCategory = ({ language, category }: ProductCategoryProps) => {
  return (
    <div className="mb-8 bg-white rounded-[20px] shadow-md overflow-hidden border border-gray-200 hover:shadow-xl transition-all duration-300 p-8 min-h-[350px]">
      <h2 className="text-xl font-bold mb-4 text-gray-800 border-b pb-2 whitespace-nowrap overflow-hidden text-overflow-ellipsis text-left uppercase">
        {category.name[language]}
      </h2>
      <ul className="space-y-2">
        {category.products.map((product) => (
          <li key={product.id}>
            <Link
              to={`/products/${category.id}/${product.slug}`}
              className="text-gray-500 hover:text-green-600 transition-colors block text-left uppercase tracking-wide py-1"
            >
              {product.name[language]}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default ProductCategory;
