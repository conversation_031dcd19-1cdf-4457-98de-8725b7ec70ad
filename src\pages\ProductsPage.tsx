import { useState } from 'react';
import { Link } from 'react-router-dom';
import ProductCategory from '../components/ProductCategory';

interface ProductsPageProps {
  language: 'vi' | 'en';
}

const ProductsPage = ({ language }: ProductsPageProps) => {
  const translations = {
    pageTitle: {
      vi: 'SẢN PHẨM',
      en: 'PRODUCTS'
    },
    heroTitle: {
      vi: 'DANH MỤC SẢN PHẨM',
      en: 'PRODUCT CATEGORIES'
    },
    searchPlaceholder: {
      vi: 'Tìm kiếm sản phẩm...',
      en: 'Search products...'
    }
  };

  const [searchTerm, setSearchTerm] = useState('');

  // Define product categories and their products
  const productCategories = [
    {
      id: 'fresh-fruit',
      name: {
        vi: 'TRÁI CÂY TƯƠI',
        en: 'FRESH FRUIT'
      },
      products: [
        {
          id: 'avocado',
          name: {
            vi: 'BƠ',
            en: 'AVOCADO'
          },
          slug: 'avocado'
        },
        {
          id: 'banana',
          name: {
            vi: 'CHUỐI',
            en: 'BANANA'
          },
          slug: 'banana'
        },
        {
          id: 'pitaya',
          name: {
            vi: 'THANH LONG',
            en: 'PITAYA'
          },
          slug: 'pitaya'
        },
        {
          id: 'durian',
          name: {
            vi: 'SẦU RIÊNG',
            en: 'DURIAN'
          },
          slug: 'durian'
        },
        {
          id: 'jackfruit',
          name: {
            vi: 'MÍT',
            en: 'JACKFRUIT'
          },
          slug: 'jackfruit'
        },
        {
          id: 'mango',
          name: {
            vi: 'XOÀI',
            en: 'MANGO'
          },
          slug: 'mango'
        },
        {
          id: 'coconut',
          name: {
            vi: 'DỪA',
            en: 'COCONUT'
          },
          slug: 'coconut'
        },
        {
          id: 'pineapple',
          name: {
            vi: 'KHÓM',
            en: 'PINEAPPLE'
          },
          slug: 'pineapple'
        }
      ]
    },
    {
      id: 'frozen-fruit',
      name: {
        vi: 'TRÁI CÂY ĐÔNG LẠNH',
        en: 'FROZEN FRUIT'
      },
      products: [
        {
          id: 'frozen-avocado',
          name: {
            vi: 'BƠ ĐÔNG LẠNH',
            en: 'FROZEN AVOCADO'
          },
          slug: 'frozen-avocado'
        },
        {
          id: 'frozen-banana',
          name: {
            vi: 'CHUỐI ĐÔNG LẠNH',
            en: 'FROZEN BANANA'
          },
          slug: 'frozen-banana'
        },
        {
          id: 'frozen-pitaya',
          name: {
            vi: 'THANH LONG ĐÔNG LẠNH',
            en: 'FROZEN PITAYA'
          },
          slug: 'frozen-pitaya'
        },
        {
          id: 'frozen-durian',
          name: {
            vi: 'SẦU RIÊNG ĐÔNG LẠNH',
            en: 'FROZEN DURIAN'
          },
          slug: 'frozen-durian'
        },
        {
          id: 'frozen-jackfruit',
          name: {
            vi: 'MÍT ĐÔNG LẠNH',
            en: 'FROZEN JACKFRUIT'
          },
          slug: 'frozen-jackfruit'
        },
        {
          id: 'frozen-mango',
          name: {
            vi: 'XOÀI ĐÔNG LẠNH',
            en: 'FROZEN MANGO'
          },
          slug: 'frozen-mango'
        },
        {
          id: 'frozen-coconut-meat',
          name: {
            vi: 'CƠM DỪA ĐÔNG LẠNH',
            en: 'FROZEN COCONUT MEAT'
          },
          slug: 'frozen-coconut-meat'
        },
        {
          id: 'frozen-pineapple',
          name: {
            vi: 'KHÓM ĐÔNG LẠNH',
            en: 'FROZEN PINEAPPLE'
          },
          slug: 'frozen-pineapple'
        }
      ]
    },
    {
      id: 'dried-fruit',
      name: {
        vi: 'TRÁI CÂY SẤY KHÔ',
        en: 'DRIED FRUIT'
      },
      products: [
        {
          id: 'dried-banana',
          name: {
            vi: 'CHUỐI SẤY KHÔ',
            en: 'DRIED BANANA'
          },
          slug: 'dried-banana'
        },
        {
          id: 'dried-jackfruit',
          name: {
            vi: 'MÍT SẤY KHÔ',
            en: 'DRIED JACKFRUIT'
          },
          slug: 'dried-jackfruit'
        },
        {
          id: 'dried-mango',
          name: {
            vi: 'XOÀI SẤY KHÔ',
            en: 'DRIED MANGO'
          },
          slug: 'dried-mango'
        },
        {
          id: 'dried-cashew-nut',
          name: {
            vi: 'HẠT ĐIỀU SẤY KHÔ',
            en: 'DRIED CASHEW NUT'
          },
          slug: 'dried-cashew-nut'
        },
        {
          id: 'dried-carrots',
          name: {
            vi: 'CÀ RỐT SẤY KHÔ',
            en: 'DRIED CARROTS'
          },
          slug: 'dried-carrots'
        },
        {
          id: 'dried-lotus-seed',
          name: {
            vi: 'HẠT SEN SẤY KHÔ',
            en: 'DRIED LOTUS SEED'
          },
          slug: 'dried-lotus-seed'
        },
        {
          id: 'dried-coconut',
          name: {
            vi: 'DỪA SẤY KHÔ',
            en: 'DRIED COCONUT'
          },
          slug: 'dried-coconut'
        },
        {
          id: 'dried-sweet-potato',
          name: {
            vi: 'KHOAI LANG SẤY KHÔ',
            en: 'DRIED SWEET POTATO'
          },
          slug: 'dried-sweet-potato'
        }
      ]
    },
    {
      id: 'freeze-dried-fruit',
      name: {
        vi: 'TRÁI CÂY ĐÔNG KHÔ',
        en: 'FREEZE DRIED FRUIT'
      },
      products: [
        {
          id: 'fd-avocado',
          name: {
            vi: 'BƠ ĐÔNG KHÔ',
            en: 'FD AVOCADO'
          },
          slug: 'fd-avocado'
        },
        {
          id: 'fd-banana',
          name: {
            vi: 'CHUỐI ĐÔNG KHÔ',
            en: 'FD BANANA'
          },
          slug: 'fd-banana'
        },
        {
          id: 'fd-pitaya',
          name: {
            vi: 'THANH LONG ĐÔNG KHÔ',
            en: 'FD PITAYA'
          },
          slug: 'fd-pitaya'
        },
        {
          id: 'fd-durian',
          name: {
            vi: 'SẦU RIÊNG ĐÔNG KHÔ',
            en: 'FD DURIAN'
          },
          slug: 'fd-durian'
        },
        {
          id: 'fd-jackfruit',
          name: {
            vi: 'MÍT ĐÔNG KHÔ',
            en: 'FD JACKFRUIT'
          },
          slug: 'fd-jackfruit'
        },
        {
          id: 'fd-mango',
          name: {
            vi: 'XOÀI ĐÔNG KHÔ',
            en: 'FD MANGO'
          },
          slug: 'fd-mango'
        },
        {
          id: 'fd-rambutan',
          name: {
            vi: 'CHÔM CHÔM ĐÔNG KHÔ',
            en: 'FD RAMBUTAN'
          },
          slug: 'fd-rambutan'
        },
        {
          id: 'fd-pineapple',
          name: {
            vi: 'KHÓM ĐÔNG KHÔ',
            en: 'FD PINEAPPLE'
          },
          slug: 'fd-pineapple'
        }
      ]
    }
  ];

  // Filter products based on search term
  const filteredCategories = productCategories.map(category => {
    return {
      ...category,
      products: category.products.filter(product => 
        product.name[language].toLowerCase().includes(searchTerm.toLowerCase())
      )
    };
  }).filter(category => category.products.length > 0);

  return (
    <div>
      {/* Hero Section */}
      <section className="relative h-[300px] bg-cover bg-center" style={{ backgroundImage: 'url(https://images.unsplash.com/photo-1490885578174-acda8905c2c6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80)' }}>
        <div className="absolute inset-0 bg-black bg-opacity-60"></div>
        <div className="absolute inset-0 flex flex-col items-center justify-center text-white text-center px-4">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            {translations.pageTitle[language]}
          </h1>
          <div className="w-20 h-1 bg-green-600 mb-6"></div>
          <p className="text-xl max-w-2xl">
            {translations.heroTitle[language]}
          </p>
        </div>
      </section>

      {/* Products Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {/* Search */}
          <div className="mb-12">
            <div className="max-w-md mx-auto">
              <input
                type="text"
                placeholder={translations.searchPlaceholder[language]}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-600"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Product Categories */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {filteredCategories.map((category) => (
              <ProductCategory 
                key={category.id} 
                language={language} 
                category={category} 
              />
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default ProductsPage;
