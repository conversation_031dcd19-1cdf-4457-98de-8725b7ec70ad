import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { useState } from 'react';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import QuickContact from './components/QuickContact';
import ScrollToTop from './components/ScrollToTop';
import Home from './pages/Home';
import About from './pages/About';
import AboutPassion from './pages/AboutPassion';
import AboutSustainability from './pages/AboutSustainability';
// import Products from './pages/Products';
import ProductsPage from './pages/ProductsPage';
import ProductDetailPage from './pages/ProductDetailPage';
import Contact from './pages/Contact';
import contactInfo from './config/contactInfo';

function App() {
  const [language, setLanguage] = useState<'vi' | 'en'>('vi');

  const toggleLanguage = () => {
    setLanguage(prev => prev === 'vi' ? 'en' : 'vi');
  };

  return (
    <Router>
      <ScrollToTop />
      <div className="flex flex-col min-h-screen">
        <Navbar language={language} toggleLanguage={toggleLanguage} />
        <main className="flex-grow">
          <Routes>
            <Route path="/" element={<Home language={language} />} />
            <Route path="/about" element={<About language={language} />} />
            <Route path="/about/passion" element={<AboutPassion language={language} />} />
            <Route path="/about/sustainability" element={<AboutSustainability language={language} />} />
            <Route path="/products" element={<ProductsPage language={language} />} />
            <Route path="/products/:categoryId/:productId" element={<ProductDetailPage language={language} />} />
            <Route path="/contact" element={<Contact language={language} />} />
          </Routes>
        </main>
        <Footer language={language} />
        <QuickContact
          email={contactInfo.email}
          phone={contactInfo.phone}
          messengerPageId={contactInfo.messengerPageId}
        />
      </div>
    </Router>
  );
}

export default App;
