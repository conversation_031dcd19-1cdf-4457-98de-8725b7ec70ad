@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(44, 159, 253, 1);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(44, 159, 253, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(44, 159, 253, 0);
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Add a subtle bounce effect when hovering over contact buttons */
.quick-contact-container button:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease-in-out;
}

/* Add a subtle shadow to the labels */
.quick-contact-container .group:hover .absolute {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
