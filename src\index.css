@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 50;
}

/* Add padding to body to prevent content from being hidden under fixed header */
body {
  padding-top: 0; /* Will be updated with JavaScript */
}

/* Swiper custom styles */
.swiper {
  width: 100%;
  height: 100%;
  padding-bottom: 50px;
  position: relative;
}

/* Container for recommended products */
.recommended-products-container {
  position: relative;
  overflow: visible !important;
  padding: 0 10px;
}

@media (max-width: 640px) {
  .recommended-products-container {
    padding: 0 30px;
  }
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  height: auto;
}

.swiper-button-next,
.swiper-button-prev,
.custom-swiper-button-next,
.custom-swiper-button-prev {
  color: rgb(19, 104, 174) !important;
  background-color: rgba(255, 255, 255, 0.95);
  width: 40px !important;
  height: 40px !important;
  border-radius: 50%;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  transition: all 0.3s ease;
  display: flex !important;
  align-items: center;
  justify-content: center;
  opacity: 1 !important;
  visibility: visible !important;
  cursor: pointer;
}

.custom-swiper-button-prev:after,
.custom-swiper-button-next:after {
  font-family: swiper-icons;
  font-size: 18px !important;
  font-weight: bold;
  text-transform: none !important;
  letter-spacing: 0;
  font-variant: initial;
  line-height: 1;
}

.custom-swiper-button-prev:after {
  content: 'prev';
}

.custom-swiper-button-next:after {
  content: 'next';
}

.swiper-button-next:hover,
.swiper-button-prev:hover,
.custom-swiper-button-next:hover,
.custom-swiper-button-prev:hover {
  background-color: rgb(19, 104, 174);
  color: white !important;
  transform: translateY(-50%) scale(1.1);
}

.swiper-button-prev,
.custom-swiper-button-prev {
  left: -5px !important;
}

.swiper-button-next,
.custom-swiper-button-next {
  right: -5px !important;
}

@media (max-width: 640px) {
  .swiper-button-prev,
  .custom-swiper-button-prev {
    left: 0px !important;
  }

  .swiper-button-next,
  .custom-swiper-button-next {
    right: 0px !important;
  }
}

/* Responsive adjustments for navigation buttons */
@media (min-width: 768px) {
  .swiper-button-prev,
  .custom-swiper-button-prev {
    left: -15px !important;
  }

  .swiper-button-next,
  .custom-swiper-button-next {
    right: -15px !important;
  }
}

@media (min-width: 1024px) {
  .swiper-button-prev,
  .custom-swiper-button-prev {
    left: -25px !important;
  }

  .swiper-button-next,
  .custom-swiper-button-next {
    right: -25px !important;
  }
}

.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 18px !important;
  font-weight: bold;
  transition: all 0.3s ease;
}

.swiper-button-next:hover:after,
.swiper-button-prev:hover:after {
  font-size: 20px !important;
}

.swiper-pagination-bullet {
  background: rgb(19, 104, 174) !important;
  opacity: 0.5;
}

.swiper-pagination-bullet-active {
  opacity: 1;
}
