import { Link } from 'react-router-dom';

interface AboutPassionProps {
  language: 'vi' | 'en';
}

const AboutPassion = ({ language }: AboutPassionProps) => {
  const translations = {
    pageTitle: {
      vi: 'ĐAM MÊ CỦA CHÚNG TÔI',
      en: 'OUR PASSION'
    },
    heroTitle: {
      vi: 'NIỀM ĐAM MÊ VỚI TRÁI CÂY TƯƠI NGON',
      en: 'OUR PASSION FOR FRESH FRUITS'
    },
    heroContent: {
      vi: 'Chúng tôi đam mê mang đến những trái cây tươi ngon nhất từ khắp nơi trên thế giới',
      en: 'We are passionate about bringing the freshest fruits from around the world'
    },
    ourPassion: {
      title: {
        vi: 'NIỀM ĐAM MÊ CỦA CHÚNG TÔI',
        en: 'OUR PASSION'
      },
      content: {
        vi: 'Từ những vườn cây trĩu quả trên khắp Việt Nam, AB FOODS ra đời với sứ mệnh mang hương vị thuần khiết của trái cây nhiệt đới đến bàn ăn mọi gia đình trên toàn cầu. Mỗi trái cây là tinh túy của đất trời, kết tinh từ nắng, gió và bàn tay chăm sóc tận tâm của người nông dân.\n\nChúng tôi chọn lọc chỉ những "siêu trái cây" tươi ngon nhất, thu hoạch đúng độ chín và bảo quản bằng công nghệ đông lạnh nhanh để giữ trọn hương vị, dinh dưỡng. Từ sầu riêng thơm lừng, mít vàng óng đến thanh long rực rỡ, mỗi sản phẩm AB FOODS là niềm tự hào về nông sản Việt.\n\nKhông chỉ kinh doanh, chúng tôi mong muốn lan tỏa lối sống lành mạnh, kết nối con người với thiên nhiên. Mỗi sản phẩm là sự tận tâm từ khâu nguyên liệu đến chế biến, vì khách hàng xứng đáng được thưởng thức những gì tốt nhất.\n\nCùng AB FOODS khám phá hương vị thuần khiết – Vì cuộc sống tươi ngon, khỏe mạnh!',
        en: 'At FreshFruits Group, our passion is to bring the freshest fruits from around the world to Vietnamese consumers. We believe that each fruit is not just a product, but a crystallization of nature, meticulous care, and the love of farmers.\n\nThis passion has driven us to constantly search for the best growing regions, the varieties that produce the most delicious fruits, and the most sustainable farming methods. From apple orchards in Washington, orange groves in California, to cherry orchards in Chile and kiwi farms in New Zealand, we have traveled the world in search of perfect fruits.'
      }
    },
    qualityCommitment: {
      title: {
        vi: 'CAM KẾT CHẤT LƯỢNG CỦA AN BÌNH FOODS',
        en: 'QUALITY COMMITMENT OF AN BINH FOODS'
      },
      intro: {
        vi: 'Tại An Bình Foods, chúng tôi không chỉ cung cấp trái cây – chúng tôi trao gửi niềm tin, sự tận tâm và những giá trị bền vững trong từng sản phẩm. Với triết lý "Chất lượng từ nguồn cội, an toàn từ tâm huyết", chúng tôi cam kết:',
        en: 'At An Binh Foods, we don\'t just provide fruits – we deliver trust, dedication, and sustainable values in every product. With the philosophy "Quality from the source, safety from dedication", we commit to:'
      },
      commitments: [
        {
          title: {
            vi: 'NGUỒN NGUYÊN LIỆU TUYỂN CHỌN KHẮT KHE',
            en: 'STRICTLY SELECTED INGREDIENTS'
          },
          points: {
            vi: [
              'Chỉ hợp tác với những vườn trái cây đạt chuẩn về chất lượng, canh tác bền vững.',
              'Thu hoạch đúng độ chín tự nhiên, không ép chín, không chất bảo quản độc hại.'
            ],
            en: [
              'Only partnering with orchards that meet quality standards and practice sustainable farming.',
              'Harvesting at the right natural ripeness, no forced ripening, no harmful preservatives.'
            ]
          },
          icon: 'M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z',
          image: '/images/about/quality1.png'
        },
        {
          title: {
            vi: 'CÔNG NGHỆ ĐÔNG LẠNH NHANH HIỆN ĐẠI',
            en: 'MODERN QUICK FREEZING TECHNOLOGY'
          },
          points: {
            vi: [
              'Ứng dụng phương pháp BQF để giữ trọn hương vị, màu sắc và dinh dưỡng như vừa mới hái.',
              'Quy trình khép kín, đạt chuẩn HACCP, ISO 22000, đảm bảo an toàn vệ sinh tuyệt đối.'
            ],
            en: [
              'Applying BQF method to preserve flavor, color and nutrition as if freshly picked.',
              'Closed process, meeting HACCP, ISO 22000 standards, ensuring absolute hygiene safety.'
            ]
          },
          icon: 'M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z',
          image: '/images/about/anh2.jpg'
        },
        {
          title: {
            vi: 'MINH BẠCH VÀ TRÁCH NHIỆM',
            en: 'TRANSPARENCY AND RESPONSIBILITY'
          },
          points: {
            vi: [
              'Truy xuất nguồn gốc rõ ràng từ vườn đến bàn ăn.',
              'Cam kết không biến đổi gen (Non-GMO), không phẩm màu, không hương liệu nhân tạo.'
            ],
            en: [
              'Clear traceability from farm to table.',
              'Commitment to non-GMO, no artificial colors, no artificial flavors.'
            ]
          },
          icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
          image: '/images/about/anh6.jpg'
        },
        {
          title: {
            vi: 'VÌ SỨC KHỎE VÀ MÔI TRƯỜNG',
            en: 'FOR HEALTH AND ENVIRONMENT'
          },
          points: {
            vi: [
              'Bao bì thân thiện, tái chế được, giảm thiểu rác thải nhựa.',
              'Hướng đến nền nông nghiệp xanh, hỗ trợ cộng đồng nông dân Việt phát triển bền vững.'
            ],
            en: [
              'Eco-friendly, recyclable packaging, minimizing plastic waste.',
              'Aiming for green agriculture, supporting Vietnamese farming communities for sustainable development.'
            ]
          },
          icon: 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z',
          image: '/images/about/anh5.jpg'
        }
      ],
      conclusion: {
        vi: 'An Bình Foods không ngừng nỗ lực để mỗi sản phẩm không chỉ là thức quà thiên nhiên ban tặng, mà còn là lời tri ân đến khách hàng – những người xứng đáng được thưởng thức điều tốt nhất!\n\n🌿 Chọn An Bình Foods – Chọn sự an tâm từ những điều giản dị nhất! 🌿',
        en: 'An Binh Foods continuously strives to ensure each product is not just a gift from nature, but also a token of appreciation to our customers – who deserve to enjoy the best!\n\n🌿 Choose An Binh Foods – Choose peace of mind from the simplest things! 🌿'
      }
    },
    // innovation: {
    //   title: {
    //     vi: 'ĐỔI MỚI KHÔNG NGỪNG',
    //     en: 'CONTINUOUS INNOVATION'
    //   },
    //   content: {
    //     vi: 'Niềm đam mê còn thúc đẩy chúng tôi không ngừng đổi mới. Chúng tôi liên tục nghiên cứu và áp dụng những công nghệ tiên tiến nhất trong việc bảo quản và vận chuyển trái cây, đảm bảo trái cây đến tay người tiêu dùng vẫn giữ nguyên độ tươi ngon như vừa được hái.\n\nChúng tôi cũng không ngừng tìm kiếm những giống trái cây mới, những hương vị độc đáo để giới thiệu đến người tiêu dùng Việt Nam, mang đến những trải nghiệm ẩm thực phong phú và đa dạng.',
    //     en: 'Passion also drives us to constantly innovate. We continuously research and apply the most advanced technologies in fruit preservation and transportation, ensuring that fruits reach consumers as fresh as they were just picked.\n\nWe also constantly search for new fruit varieties and unique flavors to introduce to Vietnamese consumers, bringing rich and diverse culinary experiences.'
    //   }
    // }
  };

  return (
    <div>
      {/* Hero Section */}
      <section className="relative h-[400px] bg-cover bg-center"
      style={{ backgroundImage: 'url(/images/about/anh3.jpg)' }}>
        <div className="absolute inset-0 bg-black bg-opacity-60"></div>
        <div className="absolute inset-0 flex flex-col items-center justify-center text-white text-center px-4">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            {translations.pageTitle[language]}
          </h1>
          <div className="w-20 h-1 bg-red-600 mb-6"></div>
          <h2 className="text-2xl md:text-3xl font-semibold mb-2">
            {translations.heroTitle[language]}
          </h2>
          <p className="text-xl max-w-2xl">
            {translations.heroContent[language]}
          </p>
        </div>
      </section>

      {/* Our Passion Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/33 mb-8 md:mb-0 md:pr-8">
              <img
                src="/images/about/about.webp"
                alt="Our Passion"
                className="rounded-lg shadow-lg w-full h-auto"
              />
            </div>
            <div className="md:w-2/3">
              <h2 className="text-3xl font-bold mb-6 text-red-600 text-center ">
                {translations.ourPassion.title[language]}
              </h2>
              <div className="text-gray-900 space-y-4">
                {translations.ourPassion.content[language].split('\n\n').map((paragraph, index) => (
                  <p key={index} className="text-lg leading-relaxed">
                    {paragraph}
                  </p>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quality Commitment Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-8 text-red-600">
            {translations.qualityCommitment.title[language]}
          </h2>

          <p className="text-xl text-center max-w-4xl mx-auto mb-12 text-gray-700">
            {translations.qualityCommitment.intro[language]}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            {translations.qualityCommitment.commitments.map((commitment, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden transform transition-transform duration-300 hover:scale-105">
                <div className="relative h-64 overflow-hidden">
                  <img
                    src={commitment.image}
                    alt={commitment.title[language]}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                    <div className="bg-red-600 bg-opacity-90 text-white py-3 px-6 rounded-lg">
                      <h3 className="text-xl font-bold text-center">
                        {index + 1}. {commitment.title[language]}
                      </h3>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex items-start mb-4">
                    <div className="flex-shrink-0 bg-red-100 p-3 rounded-full mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={commitment.icon} />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <ul className="space-y-3">
                        {commitment.points[language].map((point, i) => (
                          <li key={i} className="flex items-start">
                            <svg className="h-5 w-5 text-red-500 mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            <span className="text-gray-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="text-gray-700 space-y-4">
              {translations.qualityCommitment.conclusion[language].split('\n\n').map((paragraph, index) => (
                <p key={index} className="text-lg leading-relaxed font-medium">
                  {paragraph}
                </p>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Innovation Section */}
      {/* <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row-reverse items-center">
            <div className="md:w-1/2 mb-8 md:mb-0 md:pl-8">
              <img
                src="/images/about/tropical_fruit_1917837551.jpg"
                alt="Innovation"
                className="rounded-lg shadow-lg w-full h-auto"
              />
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6 text-red-600">
                {translations.innovation.title[language]}
              </h2>
              <div className="text-gray-700 space-y-4">
                {translations.innovation.content[language].split('\n\n').map((paragraph, index) => (
                  <p key={index} className="text-lg leading-relaxed">
                    {paragraph}
                  </p>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section> */}

      {/* Call to Action */}
      <section className="py-16 text-red-600">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">
            {language === 'vi' ? 'KHÁM PHÁ CÁC SẢN PHẨM CỦA CHÚNG TÔI' : 'EXPLORE OUR PRODUCTS'}
          </h2>
          {/* <p className="text-xl mb-8 max-w-2xl mx-auto">
            {language === 'vi'
              ? 'Trải nghiệm những trái cây tươi ngon nhất từ FreshFruits Group ngay hôm nay.'
              : 'Experience the freshest fruits from FreshFruits Group today.'}
          </p> */}
          <Link
            to="/products"
            className="inline-block bg-white text-red-600 font-bold py-3 px-8 rounded-full hover:bg-gray-100 transition duration-300"
          >
            {language === 'vi' ? 'XEM SẢN PHẨM' : 'VIEW PRODUCTS'}
          </Link>
        </div>
      </section>
    </div>
  );
};

export default AboutPassion;
