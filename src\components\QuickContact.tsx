import React, { useState, useEffect } from 'react';
import './QuickContact.css';

interface QuickContactProps {
  email: string;
  phone: string;
  messengerPageId: string; // Facebook Page ID for Messenger (not used but kept for compatibility)
}

const QuickContact: React.FC<QuickContactProps> = ({ email, phone }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const handleEmailClick = () => {
    window.location.href = `mailto:${email}`;
  };

  const handlePhoneClick = () => {
    window.location.href = `tel:${phone}`;
  };

  // Close the menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (isExpanded && !target.closest('.quick-contact-container')) {
        setIsExpanded(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isExpanded]);

  return (
    <div className="fixed right-5 bottom-5 z-50 quick-contact-container">
      <div className={`flex flex-col-reverse items-center gap-4 mb-4 transition-all duration-300 ${isExpanded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10 pointer-events-none'}`}>
        {/* Email Button */}
        <div className="relative group flex items-center">
          <div className="absolute right-full mr-3 bg-white text-gray-800 px-3 py-1 rounded-lg shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
            {email}
          </div>
          <button
            onClick={handleEmailClick}
            className="w-14 h-14 rounded-full bg-gray-200 flex items-center justify-center shadow-lg hover:bg-gray-300 transition-all duration-200 transform hover:scale-110"
            title={`Email: ${email}`}
          >
            <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center">
              <div className="w-10 h-10 rounded-full bg-gray-500 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
          </button>
        </div>

        {/* Phone Button */}
        <div className="relative group flex items-center">
          <div className="absolute right-full mr-3 bg-white text-gray-800 px-3 py-1 rounded-lg shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
            +(84)(0252) 388-8468
          </div>
          <button
            onClick={handlePhoneClick}
            className="w-14 h-14 rounded-full bg-green-200 flex items-center justify-center shadow-lg hover:bg-green-300 transition-all duration-200 transform hover:scale-110"
            title={`Call: ${phone}`}
          >
            <div className="w-12 h-12 rounded-full bg-green-300 flex items-center justify-center">
              <div className="w-10 h-10 rounded-full bg-green-600 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
            </div>
          </button>
        </div>


      </div>

      {/* Toggle Button */}
      <button
        onClick={toggleExpand}
        className={`w-16 h-16 rounded-full bg-[rgb(44,159,253)] flex items-center justify-center shadow-lg hover:bg-[rgb(44,159,253)] transition-all duration-200 transform hover:scale-105 ${!isExpanded ? 'pulse-animation' : ''}`}
        aria-label="Toggle contact options"
      >
        <div className="w-14 h-14 rounded-full bg-[rgb(44,159,253)] flex items-center justify-center border-2 border-white">
          {isExpanded ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          )}
        </div>
      </button>
    </div>
  );
};

export default QuickContact;
